package com.example.addon.mixin;

import com.example.addon.modules.ActionCoordinator;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerInteractionManager;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import static meteordevelopment.meteorclient.MeteorClient.mc;

@Mixin(ClientPlayerInteractionManager.class)
public class ClientPlayerInteractionManagerMixin {

    private static final MinecraftClient mc = MinecraftClient.getInstance();

    @Inject(method = "attackBlock", at = @At("HEAD"), cancellable = true)
    private void onAttackBlock(BlockPos pos, Direction direction, CallbackInfoReturnable<Boolean> cir) {
        // Use ActionCoordinator for centralized validation
        if (!ActionCoordinator.validateAction(ActionCoordinator.ActionType.ATTACK_BLOCK, null, pos)) {
            cir.setReturnValue(false);

            // Optionally show feedback to player
            if (mc.player != null) {
                mc.player.sendMessage(net.minecraft.text.Text.literal("§7All block breaking blocked"), true);
            }
        }
    }

    @Inject(method = "attackEntity", at = @At("HEAD"), cancellable = true)
    private void onAttackEntity(PlayerEntity player, Entity target, CallbackInfo ci) {
        // Use ActionCoordinator for centralized validation
        if (!ActionCoordinator.validateAction(ActionCoordinator.ActionType.ATTACK_ENTITY, target, null)) {
            ci.cancel();

            // Optionally show feedback to player
            if (mc.player != null) {
                mc.player.sendMessage(net.minecraft.text.Text.literal("§7Entity attack blocked - out of range or obstructed"), true);
            }
        }
    }
}