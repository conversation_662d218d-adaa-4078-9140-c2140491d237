package com.example.addon.mixin;

import com.example.addon.modules.Autoclicker;
import com.example.addon.modules.Aimbot;
import com.example.addon.utils.RaycastUtils;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerInteractionManager;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import static meteordevelopment.meteorclient.MeteorClient.mc;

@Mixin(ClientPlayerInteractionManager.class)
public class ClientPlayerInteractionManagerMixin {

    private static final MinecraftClient mc = MinecraftClient.getInstance();

    @Inject(method = "attackBlock", at = @At("HEAD"), cancellable = true)
    private void onAttackBlock(BlockPos pos, Direction direction, CallbackInfoReturnable<Boolean> cir) {
        // Get the autoclicker module
        Autoclicker autoclicker = Modules.get().get(Autoclicker.class);

        // Check if block obstructed attacks is enabled (works for both autoclicker and manual attacks)
        if (autoclicker != null && autoclicker.shouldBlockObstructedAttacks()) {
            // Check if this block attack should be blocked due to range/obstruction
            net.minecraft.util.math.Vec3d blockCenter = net.minecraft.util.math.Vec3d.ofCenter(pos);

            // Use the same validation logic as the autoclicker
            if (shouldBlockBlockAttack(blockCenter, autoclicker)) {
                // Cancel the block attack packet
                cir.setReturnValue(false);

                // Optionally show feedback to player
                if (mc.player != null) {
                    mc.player.sendMessage(net.minecraft.text.Text.literal("§7Block attack blocked - out of range or obstructed"), true);
                }
            }
        }
    }

    private boolean shouldBlockBlockAttack(net.minecraft.util.math.Vec3d blockCenter, Autoclicker autoclicker) {
        if (mc.player == null || mc.world == null) {
            return true; // Block if we can't validate
        }

        // Check range using autoclicker's attack range setting
        double distance = mc.player.getEyePos().distanceTo(blockCenter);
        if (distance > autoclicker.getAttackRange()) {
            return true; // Block - out of range
        }

        // Check if block is obstructed
        if (RaycastUtils.hasBlockObstruction(blockCenter)) {
            return true; // Block - obstructed
        }

        return false; // Allow the attack
    }
    
    @Inject(method = "attackEntity", at = @At("HEAD"), cancellable = true)
    private void onAttackEntity(PlayerEntity player, Entity target, CallbackInfo ci) {
        // Get the autoclicker module
        Autoclicker autoclicker = Modules.get().get(Autoclicker.class);

        // Check if block obstructed attacks is enabled (works for both autoclicker and manual attacks)
        if (autoclicker != null && autoclicker.shouldBlockObstructedAttacks()) {
            // Check if this entity attack should be blocked due to range/obstruction
            if (shouldBlockEntityAttack(target, autoclicker)) {
                // Cancel the entity attack packet
                ci.cancel();

                // Optionally show feedback to player
                if (mc.player != null) {
                    mc.player.sendMessage(net.minecraft.text.Text.literal("§7Entity attack blocked - out of range or obstructed"), true);
                }
            }
        }
    }

    private boolean shouldBlockEntityAttack(Entity targetEntity, Autoclicker autoclicker) {
        if (mc.player == null || mc.world == null || targetEntity == null) {
            return true; // Block if we can't validate
        }

        // Check range using autoclicker's attack range setting
        double distance = mc.player.distanceTo(targetEntity);
        if (distance > autoclicker.getAttackRange()) {
            return true; // Block - out of range
        }

        // Check if entity is obstructed by blocks
        if (RaycastUtils.isEntityObstructedByBlock(targetEntity)) {
            return true; // Block - obstructed by blocks
        }

        // Check if the player is actually facing the target using raycast
        if (!RaycastUtils.intersectsHitbox(targetEntity, autoclicker.getAttackRange())) {
            return true; // Block - not facing the target properly
        }

        return false; // Allow the attack
    }
}