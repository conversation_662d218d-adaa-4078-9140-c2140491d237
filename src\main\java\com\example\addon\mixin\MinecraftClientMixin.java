package com.example.addon.mixin;

import com.example.addon.modules.Autoclicker;
import com.example.addon.utils.RaycastUtils;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.Entity;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(MinecraftClient.class)
public class MinecraftClientMixin {
    
    @Inject(method = "doAttack", at = @At("HEAD"), cancellable = true)
    private void onDoAttack(CallbackInfoReturnable<Boolean> cir) {
        MinecraftClient mc = (MinecraftClient) (Object) this;

        // Get autoclicker module
        Autoclicker autoclicker = Modules.get().get(Autoclicker.class);
        if (autoclicker == null) {
            return; // Don't interfere if autoclicker module is not available
        }

        // Check if block obstruction blocking is enabled (works for both autoclicker and manual attacks)
        if (!autoclicker.shouldBlockObstructedAttacks()) {
            return; // Don't block if setting is disabled
        }

        // Check what we're targeting
        HitResult hitResult = mc.crosshairTarget;
        if (hitResult == null) return;

        // Handle both entity and block attacks with proper validation
        if (hitResult.getType() == HitResult.Type.ENTITY) {
            Entity targetEntity = ((net.minecraft.util.hit.EntityHitResult) hitResult).getEntity();
            if (shouldBlockEntityAttack(targetEntity, autoclicker, mc)) {
                cir.setReturnValue(false);
                if (mc.player != null) {
                    mc.player.sendMessage(net.minecraft.text.Text.literal("§7Entity attack blocked - out of range or obstructed"), true);
                }
            }
        } else if (hitResult.getType() == HitResult.Type.BLOCK) {
            // Block ALL block attacks when this setting is enabled
            cir.setReturnValue(false);
            if (mc.player != null) {
                mc.player.sendMessage(net.minecraft.text.Text.literal("§7All block breaking blocked"), true);
            }
        }
    }

    private boolean shouldBlockEntityAttack(Entity targetEntity, Autoclicker autoclicker, MinecraftClient mc) {
        if (mc.player == null || mc.world == null || targetEntity == null) {
            return true; // Block if we can't validate
        }

        // Check range using autoclicker's attack range setting
        double distance = mc.player.distanceTo(targetEntity);
        if (distance > autoclicker.getAttackRange()) {
            return true; // Block - out of range
        }

        // Check if entity is obstructed by blocks
        if (com.example.addon.utils.RaycastUtils.isEntityObstructedByBlock(targetEntity)) {
            return true; // Block - obstructed by blocks
        }

        // Check if the player is actually facing the target using raycast
        if (!com.example.addon.utils.RaycastUtils.intersectsHitbox(targetEntity, autoclicker.getAttackRange())) {
            return true; // Block - not facing the target properly
        }

        return false; // Allow the attack
    }


}