package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.AimbotUtils;
import com.example.addon.utils.InputCheckUtils;
import com.example.addon.utils.ModuleCommunication;
import com.example.addon.utils.RaycastUtils;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.entity.Entity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.registry.tag.ItemTags;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.BlockHitResult;

import com.example.addon.utils.AimbotUtils;
import com.example.addon.utils.InputCheckUtils;
import com.example.addon.utils.ModuleCommunication;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.entity.Entity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.registry.tag.ItemTags;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.BlockHitResult;
import com.example.addon.modules.InputSimulator;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import net.minecraft.client.MinecraftClient;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class Autoclicker extends Module {
   private final SettingGroup sgGeneral = settings.getDefaultGroup();

   private final Setting<Boolean> onlyWhileLMBHeld = sgGeneral.add(new BoolSetting.Builder()
        .name("only-while-LMB-held")
        .description("Only active while Left Mouse Button is held.")
        .defaultValue(true)
        .build()
    );
    
   private final Setting<Integer> clickDelay = sgGeneral.add(new IntSetting.Builder()
       .name("click-delay")
       .description("Delay between clicks in ticks (20 ticks = 1 second).")
       .defaultValue(1)
       .min(0)
       .sliderMax(20)
       .build()
   );

   private final Setting<Boolean> prioritizeCrit = sgGeneral.add(new BoolSetting.Builder()
       .name("prioritize-crit")
       .description("Wait for optimal critical hit conditions before attacking.")
       .defaultValue(true)
       .build()
   );

   private final Setting<Boolean> strictCritMode = sgGeneral.add(new BoolSetting.Builder()
       .name("strict-crit-mode")
       .description("Strict mode ensures perfect crit conditions. Lenient mode prioritizes attack speed.")
       .defaultValue(true)
       .visible(() -> prioritizeCrit.get())
       .build()
   );

   private final Setting<Integer> critWaitTicks = sgGeneral.add(new IntSetting.Builder()
       .name("crit-wait-ticks")
       .description("Maximum ticks to wait for optimal crit conditions before attacking anyway.")
       .defaultValue(5)
       .min(1)
       .max(20)
       .sliderMax(20)
       .visible(() -> prioritizeCrit.get())
       .build()
   );

   private final Setting<Boolean> allowBlockBreaking = sgGeneral.add(new BoolSetting.Builder()
       .name("allow-block-breaking")
       .description("Allow autoclicker to break blocks. When disabled, only attacks entities.")
       .defaultValue(false)
       .build()
   );

   private final Setting<Integer> blockBreakWaitTime = sgGeneral.add(new IntSetting.Builder()
       .name("block-break-wait-time")
       .description("Time to wait (in milliseconds) before allowing block breaking when first targeting a block. Prevents accidental mining.")
       .defaultValue(500)
       .min(0)
       .max(5000)
       .sliderMax(5000)
       .visible(() -> allowBlockBreaking.get())
       .build()
   );

   private final Setting<Boolean> blockObstructedAttacks = sgGeneral.add(new BoolSetting.Builder()
       .name("block-obstructed-attacks")
       .description("Block ALL block breaking and only allow entity attacks that are in range and facing the target (raycast validation).")
       .defaultValue(true)
       .build()
   );

   private final Setting<Boolean> perWeaponMinCharge = sgGeneral.add(new BoolSetting.Builder()
       .name("per-weapon-min-charge")
       .description("Enable separate minimum charge settings for different weapons.")
       .defaultValue(false)
       .build()
   );

   private final Setting<Double> minCharge = sgGeneral.add(new DoubleSetting.Builder()
       .name("min-charge")
       .description("Minimum charge for autoclicker (ignored if 'Per Weapon Min Charge' is enabled).")
       .defaultValue(0.0)
       .min(0.0)
       .sliderMax(1.0)
       .visible(() -> !perWeaponMinCharge.get())
       .build()
   );

   private final Setting<Double> swordMinCharge = sgGeneral.add(new DoubleSetting.Builder()
       .name("sword-min-charge")
       .description("Minimum charge when holding a sword.")
       .defaultValue(0.8)
       .min(0.0)
       .sliderMax(1.0)
       .visible(() -> perWeaponMinCharge.get())
       .build()
   );

   private final Setting<Double> axeMinCharge = sgGeneral.add(new DoubleSetting.Builder()
       .name("axe-min-charge")
       .description("Minimum charge when holding an axe.")
       .defaultValue(0.9)
       .min(0.0)
       .sliderMax(1.0)
       .visible(() -> perWeaponMinCharge.get())
       .build()
   );

   private final Setting<Double> maceMinCharge = sgGeneral.add(new DoubleSetting.Builder()
       .name("mace-min-charge")
       .description("Minimum charge when holding a mace.")
       .defaultValue(0.7)
       .min(0.0)
       .sliderMax(1.0)
       .visible(() -> perWeaponMinCharge.get())
       .build()
   );

   private final Setting<Boolean> hitSelect = sgGeneral.add(new BoolSetting.Builder()
       .name("hit-select")
       .description("Only attacks after being hit, waiting for crit opportunity from knockback.")
       .defaultValue(false)
       .build()
   );
    private final Setting<Integer> hitSelectCombatTimeout = sgGeneral.add(new IntSetting.Builder()
    .name("hit-select-combat-timeout")
    .description("Seconds to wait after last damage before allowing another hit select.")
    .defaultValue(7)
    .min(1)
    .max(30)
    .sliderMax(30)
    .visible(() -> hitSelect.get())
    .build()
    );

    private final Setting<Double> hitSelectCombatDistance = sgGeneral.add(new DoubleSetting.Builder()
    .name("hit-select-combat-distance")
    .description("Distance from combat location to reset hit select.")
    .defaultValue(15.0)
    .min(5.0)
    .max(50.0)
    .sliderMax(50.0)
    .visible(() -> hitSelect.get())
    .build()
    );

    private final Setting<Double> attackRange = sgGeneral.add(new DoubleSetting.Builder()
        .name("attack-range")
        .description("Maximum range for attacking entities.")
        .defaultValue(4.0)
        .min(1.0)
        .max(6.0)
        .sliderMax(6.0)
        .build()
    );
   private enum AttackMode {
       Packet,
       KeyPress,
       LeftClick,
       Input
   }
   
   private final Setting<AttackMode> attackMode = sgGeneral.add(new EnumSetting.Builder<AttackMode>()
       .name("attack-mode")
       .description("The method used to attack targets. LeftClick simulates mouse input.")
       .defaultValue(AttackMode.LeftClick)
       .build()
   );
   
   private int ticksSinceLastClick = 0;
   private int ticksWaitingForCrit = 0;
   private long blockTargetStartTime = 0;
   private long lastDamageTime = 0;
    private boolean hitSelectUsed = false;
    private double combatX = 0;
    private double combatY = 0;
    private double combatZ = 0;
   private double previousY = 0.0;
   private double previousVelocityY = 0.0;
   private boolean wasAirborne = false;
   private long lastLogTime = 0;
   private final long LOG_RATE_LIMIT_MS = 100;
   private String lastLoggedMessageContent = "";
   private static final double CRIT_VELOCITY_THRESHOLD = -0.0784;
   
   private boolean isFalling = false;
   private int fallTicks = 0;
   private static final int FALL_WAIT_TICKS = 2;
   
   private Method doAttackMethod = null;
   private boolean hasBeenHit = false;
   private float lastHealth = 20.0f;
   private float lastAbsorption = 0.0f;
   private int hurtTime = 0;
   private int lastHurtTime = 0;
   
   public Autoclicker() {
       super(AddonTemplate.CATEGORY, "autoclicker", "Acts as a triggerbot, clicking automatically.");
       
       try {
           doAttackMethod = MinecraftClient.class.getDeclaredMethod("method_1536");
           doAttackMethod.setAccessible(true);
       } catch (NoSuchMethodException e) {
           error("Failed to find Minecraft's attack method via reflection. KeyPress mode may not work.");
           e.printStackTrace();
       }
   }

   private void logAutoclickerDebug(String message) {
       long currentTime = System.currentTimeMillis();
       if (!message.equals(lastLoggedMessageContent) || (currentTime - lastLogTime > LOG_RATE_LIMIT_MS)) {
           lastLoggedMessageContent = message;
           lastLogTime = currentTime;
       }
   }

   private void checkForDamage() {
    if (mc.player == null) return;
    
    float currentHealth = mc.player.getHealth();
    float currentAbsorption = mc.player.getAbsorptionAmount();
    int currentHurtTime = mc.player.hurtTime;
    
    boolean healthDecrease = currentHealth < lastHealth;
    boolean absorptionDecrease = currentAbsorption < lastAbsorption;
    boolean gotHurt = currentHurtTime > lastHurtTime && currentHurtTime > 0;
    
    if (healthDecrease || absorptionDecrease || gotHurt) {
        long currentTime = System.currentTimeMillis();
        lastDamageTime = currentTime;
        
        if (!hitSelectUsed) {
            hasBeenHit = true;
            combatX = mc.player.getX();
            combatY = mc.player.getY();
            combatZ = mc.player.getZ();
            logAutoclickerDebug("Player damaged, hitSelect activated.");
        }
    }
    
    lastHealth = currentHealth;
    lastAbsorption = currentAbsorption;
    lastHurtTime = currentHurtTime;
    }

    private boolean isOutOfCombat() {
    if (mc.player == null) return true;
    
    long currentTime = System.currentTimeMillis();
    long timeSinceLastDamage = currentTime - lastDamageTime;
    boolean timeoutReached = timeSinceLastDamage >= (hitSelectCombatTimeout.get() * 1000L);
    
    double distanceFromCombat = Math.sqrt(
        Math.pow(mc.player.getX() - combatX, 2) + 
        Math.pow(mc.player.getY() - combatY, 2) + 
        Math.pow(mc.player.getZ() - combatZ, 2)
    );
    boolean farFromCombat = distanceFromCombat >= hitSelectCombatDistance.get();
    
    return timeoutReached || farFromCombat;
    }
    public boolean shouldBlockObstructedAttacks() {
        return blockObstructedAttacks.get();
    }

    public double getAttackRange() {
        return attackRange.get();
    }

    /**
     * Validates if an attack should be allowed based on range and line-of-sight
     * @param targetEntity The entity being attacked (null for block attacks)
     * @param targetPos The position being attacked (for block attacks)
     * @return true if the attack should be blocked, false if it should be allowed
     */
    private boolean shouldBlockAttack(Entity targetEntity, net.minecraft.util.math.Vec3d targetPos) {
        if (!blockObstructedAttacks.get()) {
            return false; // Don't block if the setting is disabled
        }

        if (mc.player == null || mc.world == null) {
            return true; // Block if we can't validate
        }

        // For entity attacks
        if (targetEntity != null) {
            // Check range
            double distance = mc.player.distanceTo(targetEntity);
            if (distance > attackRange.get()) {
                return true; // Block - out of range
            }

            // Check if entity is obstructed by blocks
            if (RaycastUtils.isEntityObstructedByBlock(targetEntity)) {
                return true; // Block - obstructed by blocks
            }

            // Check if the player is actually facing the target using raycast
            if (!RaycastUtils.intersectsHitbox(targetEntity, attackRange.get())) {
                return true; // Block - not facing the target properly
            }
        }
        // For block attacks
        else if (targetPos != null) {
            // Check range
            double distance = mc.player.getEyePos().distanceTo(targetPos);
            if (distance > attackRange.get()) {
                return true; // Block - out of range
            }

            // Check if block is obstructed
            if (RaycastUtils.hasBlockObstruction(targetPos)) {
                return true; // Block - obstructed
            }
        }

        return false; // Allow the attack
    }
    private boolean canCriticalHit() {
        if (mc.player == null) return false;
        
        if (!prioritizeCrit.get()) {
            return true;
        }
        
        double currentVelocityY = mc.player.getVelocity().y;
        
        boolean hasOptimalCritVelocity = currentVelocityY < CRIT_VELOCITY_THRESHOLD;
        
        boolean isOnGround = mc.player.isOnGround();
        boolean isInLiquid = mc.player.isInLava() || mc.player.isSubmergedInWater();
        boolean isClimbing = mc.player.isClimbing();
        boolean hasBlindness = mc.player.hasStatusEffect(net.minecraft.entity.effect.StatusEffects.BLINDNESS);
        boolean isRiding = mc.player.hasVehicle();
        
        boolean basicCritConditions = !isOnGround && !isInLiquid && !isClimbing && !hasBlindness && !isRiding;
        
        if (hitSelect.get() && hasBeenHit) {
            boolean wasGoingUp = previousVelocityY > 0;
            boolean nowGoingDown = currentVelocityY < 0;
            boolean reachedPeak = wasGoingUp && nowGoingDown;
            
            if (!basicCritConditions) {
                return false;
            }
            
            if (!reachedPeak && currentVelocityY >= 0) {
                logAutoclickerDebug("HIT SELECT: Still going up from knockback, waiting for peak");
                return false;
            }
            
            if (reachedPeak || (currentVelocityY < 0 && hasOptimalCritVelocity)) {
                logAutoclickerDebug("HIT SELECT: Perfect crit moment after knockback - attacking");
                return true;
            }
            
            if (currentVelocityY < 0 && basicCritConditions) {
                logAutoclickerDebug("HIT SELECT: Good enough crit conditions after knockback - attacking");
                return true;
            }
            
            return false;
        }
        
        boolean shouldWait = shouldWaitForCriticalHit(basicCritConditions, hasOptimalCritVelocity, currentVelocityY);
        
        if (shouldWait) {
            logAutoclickerDebug("Waiting for better crit conditions. Current velocity: " + String.format("%.4f", currentVelocityY));
            return false;
        }
        
        if (basicCritConditions && hasOptimalCritVelocity) {
            logAutoclickerDebug("Perfect crit conditions met - attacking with velocity: " + String.format("%.4f", currentVelocityY));
            ticksWaitingForCrit = 0;
        } else if (basicCritConditions) {
            logAutoclickerDebug("Basic crit conditions met, sub-optimal velocity - attacking anyway: " + String.format("%.4f", currentVelocityY));
            ticksWaitingForCrit = 0;
        } else {
            logAutoclickerDebug("No crit conditions possible - attacking normally");
            ticksWaitingForCrit = 0;
        }
        
        return true;
        }
   
   private boolean shouldWaitForCrit(double aps, double timeToFall) {
       final double CRIT_DAMAGE_MULTIPLIER = 1.5;
       final double NORMAL_DAMAGE_MULTIPLIER = 1.0;
       
       double timeBetweenAttacks = 1.0 / aps;
       
       double timeForCritScenario = timeToFall + timeBetweenAttacks;
       double damageFromCritScenario = CRIT_DAMAGE_MULTIPLIER + NORMAL_DAMAGE_MULTIPLIER;
       
       double timeForDoubleNormal = 2.0 * timeBetweenAttacks;
       double damageFromDoubleNormal = 2.0 * NORMAL_DAMAGE_MULTIPLIER;
       
       double critScenarioDPS = damageFromCritScenario / timeForCritScenario;
       double doubleNormalDPS = damageFromDoubleNormal / timeForDoubleNormal;
       
       return critScenarioDPS > doubleNormalDPS;
   }
   
   private DecisionAnalysis analyzeDecision(double aps, double timeToFall) {
       final double CRIT_DAMAGE_MULTIPLIER = 1.5;
       final double NORMAL_DAMAGE_MULTIPLIER = 1.0;
       
       double timeBetweenAttacks = 1.0 / aps;
       
       double critTime = timeToFall + timeBetweenAttacks;
       double critDamage = CRIT_DAMAGE_MULTIPLIER + NORMAL_DAMAGE_MULTIPLIER;
       double critDPS = critDamage / critTime;
       
       double normalTime = 2.0 * timeBetweenAttacks;
       double normalDamage = 2.0 * NORMAL_DAMAGE_MULTIPLIER;
       double doubleNormalDPS = normalDamage / normalTime;
       
       boolean shouldWait = critDPS > doubleNormalDPS;
       
       return new DecisionAnalysis(shouldWait, critDPS, doubleNormalDPS, critTime, normalTime, critDamage, normalDamage);
   }
   
   private double findBreakevenFallTime(double aps) {
       return 1.5 / aps;
   }
   
   private static class DecisionAnalysis {
       public final boolean shouldWaitForCrit;
       public final double critDPS;
       public final double normalDPS;
       public final double critTime;
       public final double normalTime;
       public final double critDamage;
       public final double normalDamage;
       
       public DecisionAnalysis(boolean shouldWait, double critDPS, double normalDPS, 
                             double critTime, double normalTime, double critDamage, double normalDamage) {
           this.shouldWaitForCrit = shouldWait;
           this.critDPS = critDPS;
           this.normalDPS = normalDPS;
           this.critTime = critTime;
           this.normalTime = normalTime;
           this.critDamage = critDamage;
           this.normalDamage = normalDamage;
       }
       
       @Override
       public String toString() {
           return String.format("Decision: %s | Crit: %.2f DPS (%.1f dmg in %.2fs) | Normal: %.2f DPS (%.1f dmg in %.2fs)",
               shouldWaitForCrit ? "WAIT" : "ATTACK NOW", 
               critDPS, critDamage, critTime,
               normalDPS, normalDamage, normalTime);
       }
   }
   
   private boolean shouldWaitForCriticalHit(boolean basicCritConditions, boolean hasOptimalCritVelocity, double currentVelocityY) {
       if (basicCritConditions && hasOptimalCritVelocity) {
           return false;
       }
       
       if (!basicCritConditions) {
           return false;
       }
       
       updateFallingState();
       
       double aps = getItemAttackSpeed();
       
       if (strictCritMode.get() && !hasOptimalCritVelocity) {
           if (isFalling && fallTicks < FALL_WAIT_TICKS) {
               logAutoclickerDebug("STRICT: Waiting for fall. Fall ticks: " + fallTicks + "/" + FALL_WAIT_TICKS);
               return true;
           }
           
           double timeToFall = (double) fallTicks / 20.0;
           
           boolean shouldWaitForCrit = shouldWaitForCrit(aps, timeToFall);
           
           if (shouldWaitForCrit && ticksWaitingForCrit < critWaitTicks.get()) {
               logAutoclickerDebug("STRICT: Waiting for better crit based on DPS analysis. APS: " + String.format("%.2f", aps) + ", Time to fall: " + String.format("%.3f", timeToFall));
               return true;
           }
       }
       
       if (!strictCritMode.get() && basicCritConditions && !hasOptimalCritVelocity) {
           boolean earlyInFall = Math.abs(currentVelocityY) < Math.abs(CRIT_VELOCITY_THRESHOLD) * 0.5;
           boolean stillAccelerating = currentVelocityY < 0 && mc.player.fallDistance < 2.0;
           
           if (earlyInFall && stillAccelerating && ticksWaitingForCrit < critWaitTicks.get() / 2) {
               logAutoclickerDebug("LENIENT: Early in fall, waiting briefly for better velocity. Current: " + String.format("%.4f", currentVelocityY));
               return true;
           }
       }
       
       return false;
   }
   
   private double getItemAttackSpeed() {
       if (mc.player == null) return 1.0;
       
       ItemStack mainHandStack = mc.player.getMainHandStack();
       if (mainHandStack == null || mainHandStack.isEmpty()) {
           return 1.0;
       }
       
       if (mainHandStack.isIn(ItemTags.SWORDS)) {
           return 1.6;
       } else if (mainHandStack.isIn(ItemTags.AXES)) {
           return 1.0;
       } else if (mainHandStack.getItem() == Items.MACE) {
           return 1.0;
       } else if (mainHandStack.getItem() == Items.STICK) {
           return 2.0;
       } else {
           return 1.0;
       }
   }
   
   private void updateFallingState() {
       if (mc.player == null) return;
       
       boolean currentlyFalling = !mc.player.isOnGround() && mc.player.getVelocity().y < 0;
       
       if (currentlyFalling && !isFalling) {
           isFalling = true;
           fallTicks = 0;
       } else if (isFalling && currentlyFalling) {
           fallTicks++;
       } else if (isFalling && !currentlyFalling) {
           isFalling = false;
           fallTicks = 0;
       } else if (!isFalling && !currentlyFalling) {
           fallTicks = 0;
       }
   }

   private void performLeftClick() {
       if (mc.options.attackKey != null) {
           KeyBinding.onKeyPressed(mc.options.attackKey.getDefaultKey());
       }
   }

   @Override
   public void onActivate() {
       super.onActivate();
       ticksSinceLastClick = 0;
       ticksWaitingForCrit = 0;
       blockTargetStartTime = 0;
       isFalling = false;
       fallTicks = 0;
       if (mc.player != null) {
           previousY = mc.player.getY();
           previousVelocityY = mc.player.getVelocity().y;
           wasAirborne = !mc.player.isOnGround();
           lastHealth = mc.player.getHealth();
           lastAbsorption = mc.player.getAbsorptionAmount();
           lastHurtTime = mc.player.hurtTime;
       }
       if (mc.options != null && mc.options.attackKey != null) {
           KeyBinding.setKeyPressed(mc.options.attackKey.getDefaultKey(), false);
       }
       hasBeenHit = false;
   }

   @EventHandler
    private void onTick(TickEvent.Pre event) {
    if (mc.player == null || mc.world == null || mc.interactionManager == null || mc.options == null || mc.options.attackKey == null) {
        return;
    }

    checkForDamage();

    if (hitSelectUsed && isOutOfCombat()) {
        hitSelectUsed = false;
        hasBeenHit = false;
        logAutoclickerDebug("Out of combat, hit select reset.");
    }

    ticksSinceLastClick++;

    boolean shouldClick = true;
    boolean hasValidTarget = false;
    boolean isBlockTarget = false;
    HitResult hitResult = mc.crosshairTarget;

    if (hitSelect.get() && !hasBeenHit && !hitSelectUsed) {
        shouldClick = false;
    }

    if (onlyWhileLMBHeld.get() && !InputCheckUtils.isLMBHeld()) {
        shouldClick = false;
    }

    if (ticksSinceLastClick < clickDelay.get()) {
        shouldClick = false;
    }

    boolean critConditionsMet = canCriticalHit();
    if (prioritizeCrit.get() && !critConditionsMet) {
        ticksWaitingForCrit++;
        
        if (ticksWaitingForCrit >= critWaitTicks.get()) {
            ticksWaitingForCrit = 0;
        } else {
            shouldClick = false;
        }
    } else if (prioritizeCrit.get() && critConditionsMet) {
        ticksWaitingForCrit = 0;
    }

    double currentMinCharge = minCharge.get();
    if (perWeaponMinCharge.get()) {
        ItemStack mainHandStack = mc.player.getMainHandStack();
        if (mainHandStack == null || mainHandStack.isEmpty()) {
            currentMinCharge = minCharge.get();
        } else if (mainHandStack.isIn(ItemTags.SWORDS)) {
            currentMinCharge = swordMinCharge.get();
        } else if (mainHandStack.isIn(ItemTags.AXES)) {
            currentMinCharge = axeMinCharge.get();
        } else if (mainHandStack.getItem() == Items.MACE) {
            currentMinCharge = maceMinCharge.get();
        } else {
            currentMinCharge = minCharge.get();
        }
    }

    double attackCooldown = mc.player.getAttackCooldownProgress(0.0F);
    if (attackCooldown < currentMinCharge) {
        shouldClick = false;
    }

    if (hitResult != null) {
        if (hitResult.getType() == HitResult.Type.ENTITY) {
            Entity targetEntity = ((EntityHitResult) hitResult).getEntity();
            if (targetEntity != null && !targetEntity.equals(mc.player) && targetEntity.isAlive()) {
                // Check if attack should be blocked due to range/obstruction
                if (shouldBlockAttack(targetEntity, null)) {
                    hasValidTarget = false;
                    blockTargetStartTime = 0;
                } else if (AimbotUtils.shouldAutoclickerAttackEntity(targetEntity) && mc.player.distanceTo(targetEntity) <= attackRange.get()) {
                    hasValidTarget = true;
                    blockTargetStartTime = 0;
                }
            }
        } else if (hitResult.getType() == HitResult.Type.BLOCK) {
            isBlockTarget = true;

            // If block obstructed attacks is enabled, block ALL block breaking
            if (blockObstructedAttacks.get()) {
                hasValidTarget = false;
                blockTargetStartTime = 0;
            } else if (allowBlockBreaking.get() && AimbotUtils.shouldAutoclickerAttackBlock()) {
                long currentTime = System.currentTimeMillis();

                if (blockTargetStartTime == 0) {
                    blockTargetStartTime = currentTime;
                }

                long timeWaitingMs = currentTime - blockTargetStartTime;

                if (timeWaitingMs >= blockBreakWaitTime.get()) {
                    hasValidTarget = true;
                } else {
                    hasValidTarget = false;
                }
            } else {
                hasValidTarget = false;
                blockTargetStartTime = 0;
            }
        } else {
            blockTargetStartTime = 0;
        }
    } else {
        blockTargetStartTime = 0;
    }

    if (shouldClick && hasValidTarget) {
        if (hitResult.getType() == HitResult.Type.ENTITY) {
            Entity targetEntity = ((EntityHitResult) hitResult).getEntity();

            ModuleCommunication.requestSprintReset(targetEntity);

            if (attackMode.get() == AttackMode.LeftClick) {
                performLeftClick();
            } else if (attackMode.get() == AttackMode.KeyPress) {
                if (doAttackMethod != null) {
                    try {
                        doAttackMethod.invoke(mc);
                    } catch (IllegalAccessException | InvocationTargetException e) {
                        error("Failed to invoke 'doAttack' method: " + e.getMessage());
                        mc.interactionManager.attackEntity(mc.player, targetEntity);
                        mc.player.swingHand(mc.player.getActiveHand());
                    }
                } else {
                    mc.interactionManager.attackEntity(mc.player, targetEntity);
                    mc.player.swingHand(mc.player.getActiveHand());
                }
            } else if (attackMode.get() == AttackMode.Input) {
                InputSimulator.simulateLeftClickPress();
            } else {
                mc.interactionManager.attackEntity(mc.player, targetEntity);
                mc.player.swingHand(mc.player.getActiveHand());
            }
        } else if (hitResult.getType() == HitResult.Type.BLOCK) {
            BlockHitResult blockHit = (BlockHitResult) hitResult;
            
            if (attackMode.get() == AttackMode.LeftClick) {
                performLeftClick();
            } else if (attackMode.get() == AttackMode.KeyPress) {
                if (doAttackMethod != null) {
                    try {
                        doAttackMethod.invoke(mc);
                    } catch (IllegalAccessException | InvocationTargetException e) {
                        error("Failed to invoke 'doAttack' method: " + e.getMessage());
                        mc.interactionManager.attackBlock(blockHit.getBlockPos(), blockHit.getSide());
                        mc.player.swingHand(mc.player.getActiveHand());
                    }
                } else {
                    mc.interactionManager.attackBlock(blockHit.getBlockPos(), blockHit.getSide());
                    mc.player.swingHand(mc.player.getActiveHand());
                }
            } else {
                mc.interactionManager.attackBlock(blockHit.getBlockPos(), blockHit.getSide());
                mc.player.swingHand(mc.player.getActiveHand());
            }
            
            blockTargetStartTime = 0;
        }
        
        ticksSinceLastClick = 0;
        if (hitSelect.get() && hasBeenHit) {
            hasBeenHit = false;
            hitSelectUsed = true;
            logAutoclickerDebug("Hit select attack completed, entering cooldown.");
        }
    }

    previousY = mc.player.getY();
    previousVelocityY = mc.player.getVelocity().y;
    wasAirborne = !mc.player.isOnGround();
    }

   @Override
   public void onDeactivate() {
       hasBeenHit = false;
       if (mc.options != null && mc.options.attackKey != null) {
           KeyBinding.setKeyPressed(mc.options.attackKey.getDefaultKey(), false);
       }
       super.onDeactivate();
   }
}